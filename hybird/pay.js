console.log('pay')
class Pay {
  constructor() {
    console.log('pay初始化')
  }
  pay(product_id, orderId) {
    console.log('pay', product_id, orderId)
    return new Promise((resolve, reject) => {
      if (window.flutter_inappwebview) {
        window.flutterObj.googlePay(product_id, orderId).then((res) => {
          resolve(res)
        }).catch((err) => {
          reject(err)
        })
      } else {
        resolve(true)
      }
    })
  }
}

window.payObj = new Pay()