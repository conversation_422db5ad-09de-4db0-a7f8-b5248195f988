window.__require = function e(t, n, r) {
  function s(o, u) {
    if (!n[o]) {
      if (!t[o]) {
        var b = o.split("/");
        b = b[b.length - 1];
        if (!t[b]) {
          var a = "function" == typeof __require && __require;
          if (!u && a) return a(b, !0);
          if (i) return i(b, !0);
          throw new Error("Cannot find module '" + o + "'");
        }
        o = b;
      }
      var f = n[o] = {
        exports: {}
      };
      t[o][0].call(f.exports, function(e) {
        var n = t[o][1][e];
        return s(n || e);
      }, f, f.exports, e, t, n, r);
    }
    return n[o].exports;
  }
  var i = "function" == typeof __require && __require;
  for (var o = 0; o < r.length; o++) s(r[o]);
  return s;
}({
  YouaiSDK: [ function(require, module, exports) {
    "use strict";
    cc._RF.push(module, "3f74cRjDqRCS7IqGQ4gQfdx", "YouaiSDK");
    "use strict";
    var __extends = this && this.__extends || function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || {
          __proto__: []
        } instanceof Array && function(d, b) {
          d.__proto__ = b;
        } || function(d, b) {
          for (var p in b) Object.prototype.hasOwnProperty.call(b, p) && (d[p] = b[p]);
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = null === b ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __decorate = this && this.__decorate || function(decorators, target, key, desc) {
      var c = arguments.length, r = c < 3 ? target : null === desc ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
      if ("object" === typeof Reflect && "function" === typeof Reflect.decorate) r = Reflect.decorate(decorators, target, key, desc); else for (var i = decorators.length - 1; i >= 0; i--) (d = decorators[i]) && (r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r);
      return c > 3 && r && Object.defineProperty(target, key, r), r;
    };
    var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = this && this.__generator || function(thisArg, body) {
      var _ = {
        label: 0,
        sent: function() {
          if (1 & t[0]) throw t[1];
          return t[1];
        },
        trys: [],
        ops: []
      }, f, y, t, g;
      return g = {
        next: verb(0),
        throw: verb(1),
        return: verb(2)
      }, "function" === typeof Symbol && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([ n, v ]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
          if (f = 1, y && (t = 2 & op[0] ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y),
          0) : y.next) && !(t = t.call(y, op[1])).done) return t;
          (y = 0, t) && (op = [ 2 & op[0], t.value ]);
          switch (op[0]) {
           case 0:
           case 1:
            t = op;
            break;

           case 4:
            _.label++;
            return {
              value: op[1],
              done: false
            };

           case 5:
            _.label++;
            y = op[1];
            op = [ 0 ];
            continue;

           case 7:
            op = _.ops.pop();
            _.trys.pop();
            continue;

           default:
            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (6 === op[0] || 2 === op[0])) {
              _ = 0;
              continue;
            }
            if (3 === op[0] && (!t || op[1] > t[0] && op[1] < t[3])) {
              _.label = op[1];
              break;
            }
            if (6 === op[0] && _.label < t[1]) {
              _.label = t[1];
              t = op;
              break;
            }
            if (t && _.label < t[2]) {
              _.label = t[2];
              _.ops.push(op);
              break;
            }
            t[2] && _.ops.pop();
            _.trys.pop();
            continue;
          }
          op = body.call(thisArg, _);
        } catch (e) {
          op = [ 6, e ];
          y = 0;
        } finally {
          f = t = 0;
        }
        if (5 & op[0]) throw op[1];
        return {
          value: op[0] ? op[1] : void 0,
          done: true
        };
      }
    };
    var __read = this && this.__read || function(o, n) {
      var m = "function" === typeof Symbol && o[Symbol.iterator];
      if (!m) return o;
      var i = m.call(o), r, ar = [], e;
      try {
        while ((void 0 === n || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
      } catch (error) {
        e = {
          error: error
        };
      } finally {
        try {
          r && !r.done && (m = i["return"]) && m.call(i);
        } finally {
          if (e) throw e.error;
        }
      }
      return ar;
    };
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.YouaiSDK = void 0;
    var DataReportingInterface_1 = require("../../app/game/ctrl/DataReporting/DataReportingInterface");
    var DataReportingHelperMgr_1 = require("../../app/game/ctrl/DataReporting/helper/DataReportingHelperMgr");
    var SDKInterface_1 = require("../../app/game/ctrl/sdk/SDKInterface");
    var SDKMgr_1 = require("../../app/game/ctrl/sdk/SDKMgr");
    var EngineMain_1 = require("../../engine/EngineMain");
    var Container_1 = require("../../framework/container/Container");
    var FW_1 = require("../../framework/FW");
    var YouaiSDK = function(_super) {
      __extends(YouaiSDK, _super);
      function YouaiSDK() {
        return null !== _super && _super.apply(this, arguments) || this;
      }
      YouaiSDK.prototype.isLocalSDK = function() {
        return true;
      };
      YouaiSDK.prototype.getUserType = function() {
        return SDKMgr_1.SDK_TYPE.YouAi;
      };
      YouaiSDK.prototype.init = function() {
        this._deviceImei = "imei";
      };
      YouaiSDK.prototype.login = function(username, password) {
        // if(username.length > 12 || username.length < 3) {
        //   return Promise.reject('verifyName')
        // }
        // if(password.length > 32 || password.length < 8) {
        //   return Promise.reject('verifyPassword')
        // }
        return __awaiter(this, void 0, Promise, function() {
          var LoginUrl, res, result, userInfo;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              LoginUrl = this.ModelMgr.ModelPublicData.getLoginUrl() + "account=" + username + "&password=" + password;
               console.log(LoginUrl, 'login')
              return [ 4, FW_1.FW.Http.get(LoginUrl, null, null, true) ];

             case 1:
              res = _a.sent();
              if (res && res.result) {
                EngineMain_1.EngineMain.saveLocalStorage("account_num", username);
                EngineMain_1.EngineMain.saveLocalStorage("password_num", password);
                userInfo = res.content;
                this.ModelMgr.ModelAccount.setUserInfo(res.content);
                result = [ true, userInfo ];
              } else result = [ false, res.content ];
              return [ 2, result ];
            }
          });
        });
      };
      YouaiSDK.prototype.PPP = function(payInfo) {
        return __awaiter(this, void 0, void 0, function() {
          var orderCode, promise;
          return __generator(this, function(_a) {
            orderCode = payInfo.orderId;
            promise = this.InternalPay(orderCode);
            return [ 2, promise ];
          });
        });
      };
      YouaiSDK.prototype.InternalPay = function(orderCode) {
        return __awaiter(this, void 0, void 0, function() {
          var internalPayUrl, promise;
          return __generator(this, function(_a) {
            internalPayUrl = this.ModelMgr.ModelPublicData.getInternalPayUrl();
            promise = new Promise(function(resolve, reject) {
              FW_1.FW.Http.get(internalPayUrl, {
                orderCode: orderCode
              }, null, true).then(function(res) {
                res && res.result ? resolve(res.tips) : reject();
              });
            });
            return [ 2, promise ];
          });
        });
      };
      YouaiSDK.prototype.Share = function(sahreType) {
        return __awaiter(this, void 0, void 0, function() {
          var promise;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              return [ 4, FW_1.FW.Task.delay(1e3) ];

             case 1:
              _a.sent();
              promise = new Promise(function(resolve, reject) {
                var data = "true";
                resolve(data);
              });
              return [ 2, promise ];
            }
          });
        });
      };
      YouaiSDK.prototype.register = function(username, password) {
        if(username.length > 12 || username.length < 3) {
          FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_3"));
          return Promise.reject('verifyName')
        }
        if(password.length > 32 || password.length < 8) {
          FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_4"));
          return Promise.reject('verifyPassword')
        }
        return __awaiter(this, void 0, Promise, function() {
          var RegisterUrl, account_info, msgTo, promise;
          var _this = this;
          return __generator(this, function(_a) {
            RegisterUrl = this.ModelMgr.ModelPublicData.getRegisterUrl() + "account=" + username + "&password=" + password;
            account_info = {
              account_num: username,
              password_num: password
            };
            promise = new Promise(function(resolve, reject) {
              FW_1.FW.Http.get(RegisterUrl, null, null, true).then(function(res) {
                if (res && res.result) {
                  msgTo = res.content.code;
                  EngineMain_1.EngineMain.saveLocalStorage("account_num", username);
                  EngineMain_1.EngineMain.saveLocalStorage("password_num", password);
                  _this.LayerAccount.setData(username, password);
                  var dataReporting = Container_1.Container.inst().getByAlias("DataReporting");
                  var state = dataReporting.GetIsReport(DataReportingInterface_1.SingleBuriedPointType.AccountRegistration);
                  state || dataReporting.BuriedPoint(DataReportingInterface_1.SingleBuriedPointType.AccountRegistration, "5", "", true);
                  resolve(msgTo);
                } else reject();
                _this.LayerAccount.SetRegisterResult(res);
              });
            });
            return [ 2, promise ];
          });
        });
      };
      YouaiSDK.prototype.loginOrRegist = function() {
        return __awaiter(this, void 0, Promise, function() {
          var account_num, password_num, result, handlerResult, loading;
          var _this = this;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              account_num = EngineMain_1.EngineMain.readLocalStorage("account_num");
              password_num = EngineMain_1.EngineMain.readLocalStorage("password_num");
              account_num && password_num && this.LayerAccount.setData(account_num, password_num);
              handlerResult = function(account, password, defer) {
                _this.login(account, password).then(function(arg) {
                  console.log(arg)
                  var _a = __read(arg, 2), success = _a[0], userInfo = _a[1];
                  console.log(success, userInfo, _a)
                  if (success && userInfo) {
                    var udid = EngineMain_1.EngineMain.readLocalStorage("UUID" + account, "");
                    if ("" == udid) {
                      var dataReporting = Container_1.Container.inst().getByAlias("DataReporting");
                      dataReporting.BuriedPoint(DataReportingInterface_1.BuriedPointType.DeviceAddition, "4");
                      EngineMain_1.EngineMain.saveLocalStorage("UUID" + account, DataReportingHelperMgr_1.DataReportingHelperMgr.instance.GetDataReportingHelper().GetUdId());
                    }
                    var dataReporting = Container_1.Container.inst().getByAlias("DataReporting");
                    dataReporting.BuriedPoint(DataReportingInterface_1.BuriedPointType.Login, "6");
                    result = userInfo;
                    _this.LayerAccount.unload();
                  } else {
                    if(userInfo.includes('密码输入错误')) {
                      FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_5"));
                    } else {
                      FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_1"));
                    }
                  }
                  defer();
                }).catch(function(e) {
                  defer();
                  e && FW_1.FW.Log.error(e);
                  if(e === 'verifyName') {
                    FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_3"));
                  } else if(e === 'verifyPassword') {
                    FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_4"));
                  } else {
                    FW_1.FW.showTip(FW_1.FW.G_W("YouaiSDK_2"));
                  }
                });
              };
              this.LayerAccount.load();
              this.LayerAccount.setRegister(function(account, password) {
                _this.register(account, password);
              });
              loading = false;
              this.LayerAccount.setLogin(function(account, password) {
                if (loading) return;
                loading = true;
                var defer = function() {
                  loading = false;
                };
                handlerResult(account, password, defer);
              });
              return [ 4, FW_1.FW.Task.waitUntil(function() {
                return null != result;
              }) ];

             case 1:
              _a.sent();
              return [ 2, result ];
            }
          });
        });
      };
      YouaiSDK.prototype.checkUpdate = function() {
        return __awaiter(this, void 0, void 0, function() {
          return __generator(this, function(_a) {
            return [ 2, "" ];
          });
        });
      };
      YouaiSDK.prototype.getServerAddress = function(serverParams) {
        return __awaiter(this, void 0, void 0, function() {
          var arr, params, host, url, res;
          return __generator(this, function(_a) {
            switch (_a.label) {
             case 0:
              arr = [];
              null != serverParams.type && arr.push("type=" + serverParams.type);
              null != serverParams.platform && arr.push("platform=" + serverParams.platform);
              null != serverParams.code && arr.push("code=" + serverParams.code);
              null != serverParams.token && arr.push("token=" + serverParams.token);
              null != serverParams.time && arr.push("time=" + serverParams.time);
              null != serverParams.isWss && arr.push("isWss=" + serverParams.isWss);
              null != serverParams.otherOpenId && arr.push("otherOpenId=" + serverParams.otherOpenId);
              null != serverParams.selectServerId && arr.push("selectServerId=" + serverParams.selectServerId);
              params = arr.join("&");
              host = this.ModelMgr.ModelPublicData.getServerAddressUrl();
              url = host + "?" + params;
              return [ 4, FW_1.FW.Http.get(url, null, null, true) ];

             case 1:
              res = _a.sent();
               console.log('role', res.content)
               window.__roleID = res.content.roleId
              if (res && res.result) return [ 2, res.content ];
              return [ 2, null ];
            }
          });
        });
      };
      YouaiSDK.prototype.switchAccount = function() {};
      YouaiSDK.prototype.Restart = function() {
        return false;
      };
      YouaiSDK.prototype.submitDataUserInfo = function(type, prams) {
        void 0 === prams && (prams = {});
        var roleVo = this.ModelMgr.ModelVO.VORoleBag.GetOne();
        if (!roleVo) {
          this.error("submitDataUserInfo VORoleBag null");
          return;
        }
      };
      YouaiSDK.__cname = "YouaiSDK";
      __decorate([ Container_1.injectField("ModelMgr") ], YouaiSDK.prototype, "ModelMgr", void 0);
      __decorate([ Container_1.injectField("ModelPublicData") ], YouaiSDK.prototype, "ModelPublicData", void 0);
      __decorate([ Container_1.injectField("AppCustomData") ], YouaiSDK.prototype, "AppCustomData", void 0);
      __decorate([ Container_1.injectField("LayerAccount") ], YouaiSDK.prototype, "LayerAccount", void 0);
      __decorate([ Container_1.injectField("CtrlPay") ], YouaiSDK.prototype, "CtrlPay", void 0);
      return YouaiSDK;
    }(SDKInterface_1.SDKInterface);
    exports.YouaiSDK = YouaiSDK;
    cc._RF.pop();
  }, {
    "../../app/game/ctrl/DataReporting/DataReportingInterface": void 0,
    "../../app/game/ctrl/DataReporting/helper/DataReportingHelperMgr": void 0,
    "../../app/game/ctrl/sdk/SDKInterface": void 0,
    "../../app/game/ctrl/sdk/SDKMgr": void 0,
    "../../engine/EngineMain": void 0,
    "../../framework/FW": void 0,
    "../../framework/container/Container": void 0
  } ]
}, {}, [ "YouaiSDK" ]);